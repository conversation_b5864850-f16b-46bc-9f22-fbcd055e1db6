"""
🐄 Çiftlik İzleme Sistemi - Ana Program
ONVIF kameralardan hayvan da<PERSON>ışı analizi ve otomatik etiketleme

Kullanım: python main.py
"""

import time
import signal
import logging
import sys
from threading import Event
from datetime import datetime

from config import LOG_CONFIG, create_directories
from camera_handler import CameraHandler
from behavior_analyzer import BehaviorAnalyzer
from data_recorder import DataRecorder

class FarmMonitoringSystem:
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Sistem bileşenleri
        self.camera = CameraHandler()
        self.analyzer = BehaviorAnalyzer()
        self.recorder = DataRecorder()
        
        # Kontrol değişkenleri
        self.stop_event = Event()
        self.is_running = False
        
        # İstatistikler
        self.processed_frames = 0
        self.start_time = None
        
        # Graceful shutdown için signal handler
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """Logging sistemini kurar"""
        create_directories()  # Klasörleri oluştur
        
        logging.basicConfig(
            level=getattr(logging, LOG_CONFIG['level']),
            format=LOG_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOG_CONFIG['session_log'], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def signal_handler(self, signum, frame):
        """Sistem sinyallerini yakalar (Ctrl+C vb.)"""
        print(f"\n🛑 Sistem durdurma sinyali alındı: {signum}")
        self.stop()
    
    def start(self):
        """Sistemi başlatır"""
        self.logger.info("🚀 Çiftlik İzleme Sistemi başlatılıyor...")
        self.start_time = datetime.now()
        
        try:
            # 1. Kameraya bağlan
            if not self.camera.connect():
                self.logger.error("❌ Kamera bağlantısı başarısız!")
                return False
            
            # 2. Frame yakalamayı başlat
            if not self.camera.start_capture():
                self.logger.error("❌ Frame yakalama başlatılamadı!")
                return False
            
            # 3. Ana işlem döngüsü
            self.is_running = True
            self.logger.info("✅ Sistem başarıyla başlatıldı")
            self.logger.info("📹 İzleme başladı... (Durdurmak için Ctrl+C)")
            
            self.main_loop()
            
        except Exception as e:
            self.logger.error(f"❌ Sistem başlatma hatası: {e}")
            return False
        
        return True
    
    def main_loop(self):
        """Ana işlem döngüsü"""
        last_process_time = time.time()
        
        while not self.stop_event.is_set():
            try:
                current_time = time.time()
                
                # 1 saniyede bir işlem yap
                if current_time - last_process_time >= 1.0:
                    self.process_frame()
                    last_process_time = current_time
                
                # CPU kullanımını azaltmak için kısa bekleme
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Ana döngü hatası: {e}")
                time.sleep(1)
    
    def process_frame(self):
        """Tek bir frame'i işler"""
        try:
            # Frame al
            frame = self.camera.get_current_frame()
            if frame is None:
                self.logger.warning("Frame alınamadı")
                return
            
            self.processed_frames += 1
            
            # Davranış analizi yap
            analysis_result = self.analyzer.analyze_frame(frame)
            
            # Eğer davranış tespit edildiyse kaydet
            if analysis_result['has_behaviors']:
                success = self.recorder.save_frame_with_labels(frame, analysis_result)
                if success:
                    self.logger.info(f"📸 Frame kaydedildi: #{self.processed_frames}")
            
            # Her 60 saniyede bir durum raporu
            if self.processed_frames % 60 == 0:
                self.print_status()
                
        except Exception as e:
            self.logger.error(f"Frame işleme hatası: {e}")
    
    def print_status(self):
        """Sistem durumunu yazdırır"""
        uptime = datetime.now() - self.start_time if self.start_time else None
        stats = self.recorder.get_stats()
        
        print(f"\n📊 SİSTEM DURUMU")
        print(f"⏱️  Çalışma süresi: {uptime}")
        print(f"🎬 İşlenen frame: {self.processed_frames}")
        print(f"💾 Kaydedilen frame: {stats['total_frames_saved']}")
        print(f"🔍 Tespit oranı: {stats['total_frames_saved']}/{self.processed_frames} ({(stats['total_frames_saved']/max(self.processed_frames,1)*100):.1f}%)")
        
        # Davranış istatistikleri
        behaviors = stats['behaviors_detected']
        active_behaviors = {k: v for k, v in behaviors.items() if v > 0}
        if active_behaviors:
            print("🐄 Tespit edilen davranışlar:")
            for behavior, count in active_behaviors.items():
                print(f"   - {behavior}: {count}")
        print("-" * 40)
    
    def stop(self):
        """Sistemi durdurur"""
        if not self.is_running:
            return
            
        self.logger.info("🛑 Sistem durduruluyor...")
        self.is_running = False
        self.stop_event.set()
        
        # Bileşenleri durdur
        self.camera.stop()
        
        # Son istatistikleri kaydet
        self.recorder.save_session_stats()
        self.recorder.print_stats()
        
        self.logger.info("✅ Sistem başarıyla durduruldu")

def main():
    """Ana fonksiyon"""
    print("🐄 Çiftlik İzleme Sistemi v1.0")
    print("=" * 50)
    
    system = FarmMonitoringSystem()
    
    try:
        if system.start():
            # Sistem çalışırken bekle
            while system.is_running:
                time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Kullanıcı tarafından durduruldu")
    except Exception as e:
        print(f"❌ Beklenmeyen hata: {e}")
    finally:
        system.stop()

if __name__ == "__main__":
    main()
