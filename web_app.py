"""
🐄 Çiftlik İzleme Sistemi - Web Arayüzü
Flask tabanlı basit ve hoş web arayüzü
"""

import os
import json
import base64
import threading
import time
from datetime import datetime
from flask import Flask, render_template, jsonify, request
from flask_socketio import <PERSON>cket<PERSON>, emit
import cv2

from config import DATASET_CONFIG, DEMO_MODE, CAMERA_CONFIG
from camera_handler import CameraHandler
from behavior_analyzer import BehaviorAnalyzer
from data_recorder import DataRecorder

app = Flask(__name__)
app.config['SECRET_KEY'] = 'ciftlik_izleme_2025'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global değişkenler
monitoring_system = None
is_running = False
current_stats = {
    'total_frames': 0,
    'saved_frames': 0,
    'detection_rate': 0,
    'uptime': '00:00:00',
    'behaviors': {
        'cow-standing': 0,
        'cow-lying': 0,
        'calf-suckling': 0,
        'cow-ruminating': 0
    },
    'status': 'Durduruldu'
}

class WebMonitoringSystem:
    def __init__(self):
        self.camera = CameraHandler()
        self.analyzer = BehaviorAnalyzer()
        self.recorder = DataRecorder()
        self.is_running = False
        self.start_time = None
        self.processed_frames = 0
        
    def start(self):
        """Sistemi başlatır"""
        if self.is_running:
            return False
            
        if not self.camera.connect():
            return False
            
        if not self.camera.start_capture():
            return False
            
        self.is_running = True
        self.start_time = datetime.now()
        self.processed_frames = 0
        
        # Ana işlem thread'ini başlat
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        return True
    
    def stop(self):
        """Sistemi durdurur"""
        self.is_running = False
        self.camera.stop()
        if hasattr(self, 'recorder'):
            self.recorder.save_session_stats()
    
    def _monitor_loop(self):
        """Ana izleme döngüsü"""
        last_process_time = time.time()
        
        while self.is_running:
            try:
                current_time = time.time()
                
                if current_time - last_process_time >= 1.0:
                    self._process_frame()
                    last_process_time = current_time
                    
                    # İstatistikleri güncelle
                    self._update_stats()
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Monitor loop hatası: {e}")
                time.sleep(1)
    
    def _process_frame(self):
        """Tek frame işler"""
        frame = self.camera.get_current_frame()
        if frame is None:
            return
            
        self.processed_frames += 1
        
        # Davranış analizi
        analysis_result = self.analyzer.analyze_frame(frame)
        
        # Frame'i base64'e çevir (web için)
        _, buffer = cv2.imencode('.jpg', frame)
        frame_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # WebSocket ile gönder
        socketio.emit('new_frame', {
            'image': f"data:image/jpeg;base64,{frame_base64}",
            'detections': analysis_result.get('detections', []),
            'frame_number': self.processed_frames
        })
        
        # Eğer davranış tespit edildiyse kaydet
        if analysis_result['has_behaviors']:
            self.recorder.save_frame_with_labels(frame, analysis_result)
    
    def _update_stats(self):
        """İstatistikleri günceller"""
        global current_stats
        
        if self.start_time:
            uptime = datetime.now() - self.start_time
            uptime_str = str(uptime).split('.')[0]  # Mikrosaniyeleri kaldır
        else:
            uptime_str = '00:00:00'
        
        recorder_stats = self.recorder.get_stats()
        
        current_stats.update({
            'total_frames': self.processed_frames,
            'saved_frames': recorder_stats['total_frames_saved'],
            'detection_rate': round((recorder_stats['total_frames_saved'] / max(self.processed_frames, 1)) * 100, 1),
            'uptime': uptime_str,
            'behaviors': recorder_stats['behaviors_detected'],
            'status': 'Çalışıyor' if self.is_running else 'Durduruldu'
        })
        
        # WebSocket ile istatistikleri gönder
        socketio.emit('stats_update', current_stats)
    
    def get_current_frame_base64(self):
        """Güncel frame'i base64 olarak döndürür"""
        frame = self.camera.get_current_frame()
        if frame is None:
            return None
            
        _, buffer = cv2.imencode('.jpg', frame)
        return base64.b64encode(buffer).decode('utf-8')

# Flask Routes
@app.route('/')
def index():
    """Ana sayfa"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """Sistem durumunu döndürür"""
    global monitoring_system, is_running
    
    status = {
        'is_running': is_running,
        'demo_mode': DEMO_MODE,
        'camera_ip': CAMERA_CONFIG['ip'] if not DEMO_MODE else 'Webcam',
        'stats': current_stats
    }
    return jsonify(status)

@app.route('/api/start', methods=['POST'])
def start_monitoring():
    """İzlemeyi başlatır"""
    global monitoring_system, is_running
    
    if is_running:
        return jsonify({'success': False, 'message': 'Sistem zaten çalışıyor'})
    
    try:
        monitoring_system = WebMonitoringSystem()
        if monitoring_system.start():
            is_running = True
            return jsonify({'success': True, 'message': 'Sistem başlatıldı'})
        else:
            return jsonify({'success': False, 'message': 'Kamera bağlantısı başarısız'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Hata: {str(e)}'})

@app.route('/api/stop', methods=['POST'])
def stop_monitoring():
    """İzlemeyi durdurur"""
    global monitoring_system, is_running
    
    if not is_running:
        return jsonify({'success': False, 'message': 'Sistem zaten durdurulmuş'})
    
    try:
        if monitoring_system:
            monitoring_system.stop()
        is_running = False
        current_stats['status'] = 'Durduruldu'
        return jsonify({'success': True, 'message': 'Sistem durduruldu'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'Hata: {str(e)}'})

@app.route('/api/recent_images')
def get_recent_images():
    """Son kaydedilen görüntüleri döndürür"""
    try:
        images_dir = DATASET_CONFIG['images_dir']
        if not images_dir.exists():
            return jsonify([])
        
        # Son 10 görüntüyü al
        image_files = sorted(images_dir.glob('*.jpg'), key=os.path.getmtime, reverse=True)[:10]
        
        images = []
        for img_file in image_files:
            images.append({
                'filename': img_file.name,
                'timestamp': datetime.fromtimestamp(img_file.stat().st_mtime).strftime('%H:%M:%S'),
                'path': f'/static/images/{img_file.name}'
            })
        
        return jsonify(images)
    except Exception as e:
        return jsonify([])

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    """İstemci bağlandığında"""
    emit('connected', {'message': 'Bağlantı kuruldu'})
    
    # Güncel istatistikleri gönder
    emit('stats_update', current_stats)

@socketio.on('disconnect')
def handle_disconnect():
    """İstemci bağlantısı kesildiğinde"""
    print('İstemci bağlantısı kesildi')

if __name__ == '__main__':
    # Static dosyalar için images klasörünü ekle
    import shutil
    static_dir = os.path.join(os.path.dirname(__file__), 'static', 'images')
    os.makedirs(static_dir, exist_ok=True)
    
    print("🌐 Web Arayüzü Başlatılıyor...")
    print("📱 Tarayıcınızda http://localhost:5000 adresini açın")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
