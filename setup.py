"""
Çiftlik İzleme Sistemi - Kurulum ve Test Scripti
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """Logging sistemini kurar"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def check_python_version():
    """Python versiyonunu kontrol eder"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8 veya üzeri gerekli!")
        logger.error(f"Mevcut versiyon: {sys.version}")
        return False
    
    logger.info(f"✅ Python versiyonu uygun: {sys.version}")
    return True

def install_requirements():
    """Gerekli paketleri yükler"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("📦 Gerekli paketler yükleniyor...")
        
        # requirements.txt'yi yükle
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Paketler başarıyla yüklendi")
            return True
        else:
            logger.error(f"❌ Paket yükleme hatası: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Paket yükleme hatası: {e}")
        return False

def create_directories():
    """Gerekli klasörleri oluşturur"""
    logger = logging.getLogger(__name__)
    
    try:
        from config import create_directories as create_dirs
        create_dirs()
        logger.info("✅ Klasörler oluşturuldu")
        return True
    except Exception as e:
        logger.error(f"❌ Klasör oluşturma hatası: {e}")
        return False

def test_imports():
    """Gerekli modüllerin import edilebilirliğini test eder"""
    logger = logging.getLogger(__name__)
    
    test_modules = [
        'cv2',
        'numpy', 
        'ultralytics',
        'PIL'
    ]
    
    failed_imports = []
    
    for module in test_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module} modülü başarıyla import edildi")
        except ImportError as e:
            logger.error(f"❌ {module} modülü import edilemedi: {e}")
            failed_imports.append(module)
    
    return len(failed_imports) == 0

def test_yolo_model():
    """YOLOv8 modelinin yüklenebilirliğini test eder"""
    logger = logging.getLogger(__name__)
    
    try:
        from ultralytics import YOLO
        logger.info("📥 YOLOv8 modeli test ediliyor...")
        
        # Model yükle (ilk kez çalıştırıldığında indirilir)
        model = YOLO('yolov8n.pt')
        logger.info("✅ YOLOv8 modeli başarıyla yüklendi")
        
        # Test tahmini
        import numpy as np
        test_image = np.zeros((640, 480, 3), dtype=np.uint8)
        results = model(test_image, verbose=False)
        logger.info("✅ Model tahmini başarılı")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ YOLOv8 model testi başarısız: {e}")
        return False

def test_camera_config():
    """Kamera konfigürasyonunu kontrol eder"""
    logger = logging.getLogger(__name__)
    
    try:
        from config import CAMERA_CONFIG, RTSP_URL_TEMPLATE
        
        # Konfigürasyon kontrolü
        required_keys = ['ip', 'port', 'username', 'password', 'rtsp_port']
        for key in required_keys:
            if key not in CAMERA_CONFIG:
                logger.error(f"❌ Kamera konfigürasyonunda {key} eksik!")
                return False
        
        # RTSP URL oluşturma testi
        rtsp_url = RTSP_URL_TEMPLATE.format(**CAMERA_CONFIG)
        logger.info(f"✅ RTSP URL: {rtsp_url}")
        
        logger.warning("⚠️  Gerçek kamera bağlantısı test edilmedi")
        logger.warning("⚠️  Kamera ayarlarını config.py'de kontrol edin")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Kamera konfigürasyon testi başarısız: {e}")
        return False

def run_system_test():
    """Sistem bileşenlerini test eder"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🧪 Sistem bileşenleri test ediliyor...")
        
        # Modülleri import et
        from camera_handler import CameraHandler
        from behavior_analyzer import BehaviorAnalyzer
        from data_recorder import DataRecorder
        
        # Bileşenleri oluştur
        analyzer = BehaviorAnalyzer()
        recorder = DataRecorder()
        
        # Test frame
        import numpy as np
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Analiz testi
        result = analyzer.analyze_frame(test_frame)
        logger.info("✅ Davranış analizi testi başarılı")
        
        # Kayıt testi (eğer davranış tespit edilirse)
        if result['has_behaviors']:
            recorder.save_frame_with_labels(test_frame, result)
            logger.info("✅ Veri kayıt testi başarılı")
        else:
            logger.info("ℹ️  Test frame'inde davranış tespit edilmedi (normal)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Sistem testi başarısız: {e}")
        return False

def main():
    """Ana kurulum fonksiyonu"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🐄 Çiftlik İzleme Sistemi - Kurulum")
    print("=" * 50)
    
    # Test adımları
    tests = [
        ("Python Versiyon Kontrolü", check_python_version),
        ("Paket Yükleme", install_requirements),
        ("Klasör Oluşturma", create_directories),
        ("Modül Import Testi", test_imports),
        ("YOLOv8 Model Testi", test_yolo_model),
        ("Kamera Konfigürasyon Testi", test_camera_config),
        ("Sistem Bileşen Testi", run_system_test)
    ]
    
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} başarılı")
            else:
                print(f"❌ {test_name} başarısız")
                failed_tests.append(test_name)
        except Exception as e:
            print(f"❌ {test_name} hatası: {e}")
            failed_tests.append(test_name)
    
    # Sonuç raporu
    print("\n" + "=" * 50)
    print("📋 KURULUM RAPORU")
    print("=" * 50)
    
    if not failed_tests:
        print("🎉 Tüm testler başarılı!")
        print("✅ Sistem kullanıma hazır")
        print("\n🚀 Sistemi başlatmak için:")
        print("   python main.py")
    else:
        print(f"❌ {len(failed_tests)} test başarısız:")
        for test in failed_tests:
            print(f"   - {test}")
        print("\n🔧 Lütfen hataları düzeltin ve tekrar deneyin")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
