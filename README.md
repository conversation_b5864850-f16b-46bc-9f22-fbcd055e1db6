# 🐄 Çiftlik İzleme Sistemi

ONVIF uyumlu IP kameraları kullanarak hayvan davranışlarını otomatik olarak tespit eden ve etiketleyen sistem.

## 🎯 Özellikler

- **G<PERSON>çek Zamanlı İzleme**: RTSP stream üzerinden sürekli frame yakalama
- **Davranış Analizi**: YOLOv8 ile hayvan davranışı tespiti
- **Otomatik Etiketleme**: YOLO formatında etiket dosyaları oluşturma
- **Veri <PERSON>**: Tespit edilen davranışları görüntü + etiket olarak kaydetme
- **İstatistik Takibi**: Detaylı oturum raporları

## 📋 Tespit Edilen Davranışlar

- `cow-standing` - İnek ayakta
- `cow-lying` - İnek yatıyor  
- `cow-ruminating` - İnek geviş getiriyor
- `calf-suckling` - <PERSON>uzağ<PERSON> emiyor

## 🚀 Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
pip install -r requirements.txt
```

### 2. Kamera Ayarları
`config.py` dosyasında kamera bilgilerini düzenleyin:

```python
CAMERA_CONFIG = {
    "ip": "************",      # Kamera IP adresi
    "port": 80,                # ONVIF portu  
    "username": "admin",       # Kullanıcı adı
    "password": "admin123",    # Şifre
    "rtsp_port": 554          # RTSP portu
}
```

### 3. Klasör Yapısını Oluştur
```bash
python config.py
```

## 🎮 Kullanım

### Komut Satırı Modu
```bash
python main.py
```

### Web Arayüzü Modu (Önerilen)
```bash
python web_app.py
```
Tarayıcınızda `http://localhost:5000` adresini açın

### Durdurma
- `Ctrl+C` ile güvenli şekilde durdurun
- Sistem otomatik olarak istatistikleri kaydeder

## 📁 Klasör Yapısı

```
ciftlik_takip/
├── main.py                 # Ana program
├── config.py              # Konfigürasyon
├── camera_handler.py      # RTSP bağlantı modülü
├── behavior_analyzer.py   # Davranış analizi
├── data_recorder.py       # Veri kayıt modülü
├── requirements.txt       # Python bağımlılıkları
├── README.md             # Bu dosya
└── dataset/              # Veri klasörü
    ├── images/           # Kaydedilen görüntüler (.jpg)
    ├── labels/           # YOLO etiket dosyaları (.txt)
    └── logs/             # Log dosyaları
        ├── session.log   # Sistem logları
        └── camera64.json # Oturum istatistikleri
```

## 🔧 Konfigürasyon

### Kamera Ayarları
- IP adresi, port, kullanıcı adı/şifre
- RTSP URL formatı

### Frame Yakalama
- FPS: 1 (saniyede 1 frame)
- Çözünürlük: 640x480
- JPEG kalitesi: 95

### Model Ayarları
- YOLOv8 nano model (başlangıç)
- Confidence threshold: 0.5
- IoU threshold: 0.45

## 📊 İstatistikler

Sistem çalışırken:
- Her 60 saniyede durum raporu
- Toplam işlenen frame sayısı
- Kaydedilen frame sayısı
- Davranış tespit oranları

Oturum sonunda:
- Detaylı JSON raporu (`camera64.json`)
- Davranış bazında istatistikler
- Oturum süresi ve performans metrikleri

## 🐛 Sorun Giderme

### Kamera Bağlantı Sorunları
1. IP adresini kontrol edin
2. ONVIF servisinin aktif olduğundan emin olun
3. Kullanıcı adı/şifre doğruluğunu kontrol edin
4. Firewall ayarlarını kontrol edin

### Model Yükleme Sorunları
1. İnternet bağlantısını kontrol edin (ilk çalıştırmada model indirilir)
2. Disk alanının yeterli olduğundan emin olun

### Performans Sorunları
1. GPU kullanımı için CUDA kurulumu yapın
2. Frame çözünürlüğünü düşürün
3. Confidence threshold'u artırın

## 🌐 Web Arayüzü

### Özellikler
- **📹 Canlı Video İzleme**: Gerçek zamanlı kamera görüntüsü
- **📊 Anlık İstatistikler**: Frame sayısı, tespit oranı, çalışma süresi
- **🧠 Davranış Analizi**: Tespit edilen davranışların canlı takibi
- **📸 Son Görüntüler**: Kaydedilen son görüntülerin galerisi
- **🎮 Sistem Kontrolü**: Başlat/Durdur butonları
- **📱 Responsive Tasarım**: Mobil uyumlu arayüz

### Kullanım
1. Web sunucusunu başlatın: `python web_app.py`
2. Tarayıcıda `http://localhost:5000` adresini açın
3. "▶️ Başlat" butonuna tıklayın
4. Canlı video akışını ve istatistikleri izleyin

## 🔮 Gelecek Geliştirmeler

- [x] Web arayüzü ile canlı izleme ✅
- [ ] Özel hayvan davranışı modeli eğitimi
- [ ] Çoklu kamera desteği
- [ ] Alarm sistemi entegrasyonu
- [ ] Veritabanı entegrasyonu
- [ ] REST API geliştirme

## 📝 Lisans

Bu proje MIT lisansı altında geliştirilmiştir.

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 İletişim

Sorularınız için issue açabilir veya doğrudan iletişime geçebilirsiniz.
