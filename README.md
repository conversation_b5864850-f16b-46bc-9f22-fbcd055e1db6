# 🐄 Çiftlik İzleme Sistemi

ONVIF uyumlu IP kameraları kullanarak hayvan davranışlarını otomatik olarak tespit eden ve etiketleyen sistem.

## 🎯 Özellikler

- **G<PERSON>çek Zamanlı İzleme**: RTSP stream üzerinden sürekli frame yakalama
- **Davranış Analizi**: YOLOv8 ile hayvan davranışı tespiti
- **Otomatik Etiketleme**: YOLO formatında etiket dosyaları oluşturma
- **Veri <PERSON>**: Tespit edilen davranışları görüntü + etiket olarak kaydetme
- **İstatistik Takibi**: Detaylı oturum raporları

## 📋 Tespit Edilen Davranışlar

- `cow-standing` - İnek ayakta
- `cow-lying` - İnek yatıyor  
- `cow-ruminating` - İnek geviş getiriyor
- `calf-suckling` - <PERSON>uzağ<PERSON> emiyor

## 🚀 Kurulum

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
```bash
pip install -r requirements.txt
```

### 2. Kamera Ayarları
`config.py` dosyasında kamera bilgilerini düzenleyin:

```python
CAMERA_CONFIG = {
    "ip": "************",      # Kamera IP adresi
    "port": 80,                # ONVIF portu  
    "username": "admin",       # Kullanıcı adı
    "password": "admin123",    # Şifre
    "rtsp_port": 554          # RTSP portu
}
```

### 3. Klasör Yapısını Oluştur
```bash
python config.py
```

## 🎮 Kullanım

### Sistemi Başlat
```bash
python main.py
```

### Durdurma
- `Ctrl+C` ile güvenli şekilde durdurun
- Sistem otomatik olarak istatistikleri kaydeder

## 📁 Klasör Yapısı

```
ciftlik_takip/
├── main.py                 # Ana program
├── config.py              # Konfigürasyon
├── camera_handler.py      # RTSP bağlantı modülü
├── behavior_analyzer.py   # Davranış analizi
├── data_recorder.py       # Veri kayıt modülü
├── requirements.txt       # Python bağımlılıkları
├── README.md             # Bu dosya
└── dataset/              # Veri klasörü
    ├── images/           # Kaydedilen görüntüler (.jpg)
    ├── labels/           # YOLO etiket dosyaları (.txt)
    └── logs/             # Log dosyaları
        ├── session.log   # Sistem logları
        └── camera64.json # Oturum istatistikleri
```

## 🔧 Konfigürasyon

### Kamera Ayarları
- IP adresi, port, kullanıcı adı/şifre
- RTSP URL formatı

### Frame Yakalama
- FPS: 1 (saniyede 1 frame)
- Çözünürlük: 640x480
- JPEG kalitesi: 95

### Model Ayarları
- YOLOv8 nano model (başlangıç)
- Confidence threshold: 0.5
- IoU threshold: 0.45

## 📊 İstatistikler

Sistem çalışırken:
- Her 60 saniyede durum raporu
- Toplam işlenen frame sayısı
- Kaydedilen frame sayısı
- Davranış tespit oranları

Oturum sonunda:
- Detaylı JSON raporu (`camera64.json`)
- Davranış bazında istatistikler
- Oturum süresi ve performans metrikleri

## 🐛 Sorun Giderme

### Kamera Bağlantı Sorunları
1. IP adresini kontrol edin
2. ONVIF servisinin aktif olduğundan emin olun
3. Kullanıcı adı/şifre doğruluğunu kontrol edin
4. Firewall ayarlarını kontrol edin

### Model Yükleme Sorunları
1. İnternet bağlantısını kontrol edin (ilk çalıştırmada model indirilir)
2. Disk alanının yeterli olduğundan emin olun

### Performans Sorunları
1. GPU kullanımı için CUDA kurulumu yapın
2. Frame çözünürlüğünü düşürün
3. Confidence threshold'u artırın

## 🔮 Gelecek Geliştirmeler

- [ ] Özel hayvan davranışı modeli eğitimi
- [ ] Web arayüzü ile canlı izleme
- [ ] Çoklu kamera desteği
- [ ] Alarm sistemi entegrasyonu
- [ ] Veritabanı entegrasyonu
- [ ] REST API geliştirme

## 📝 Lisans

Bu proje MIT lisansı altında geliştirilmiştir.

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 İletişim

Sorularınız için issue açabilir veya doğrudan iletişime geçebilirsiniz.
