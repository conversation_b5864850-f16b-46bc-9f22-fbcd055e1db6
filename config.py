"""
Çiftlik İzleme Sistemi - Konfigürasyon Dosyası
"""

import os
from pathlib import Path

# Proje ana dizini
PROJECT_ROOT = Path(__file__).parent

# Kamera ayarları
CAMERA_CONFIG = {
    "ip": "************",  # Kamera IP adresi - Gerçek IP adresini buraya girin
    "port": 80,            # ONVIF portu
    "username": "admin",   # Kamera kullanıcı adı - <PERSON><PERSON><PERSON><PERSON> kullanıcı adını girin
    "password": "admin123", # Kamera şifresi - Gerçek şifreyi girin
    "rtsp_port": 554       # RTSP portu
}

# RTSP URL formatı
RTSP_URL_TEMPLATE = "rtsp://{username}:{password}@{ip}:{rtsp_port}/cam/realmonitor?channel=1&subtype=0"

# Demo modu (gerçek kamera yoksa webcam kullan)
DEMO_MODE = True  # False yapın gerçek RTSP kamera kullanmak için

# Veri kayıt ayarları
DATASET_CONFIG = {
    "base_dir": PROJECT_ROOT / "dataset",
    "images_dir": PROJECT_ROOT / "dataset" / "images",
    "labels_dir": PROJECT_ROOT / "dataset" / "labels", 
    "logs_dir": PROJECT_ROOT / "dataset" / "logs"
}

# Frame yakalama ayarları
CAPTURE_CONFIG = {
    "fps": 1,              # Saniyede 1 frame
    "frame_width": 640,    # Frame genişliği
    "frame_height": 480,   # Frame yüksekliği
    "quality": 95          # JPEG kalitesi
}

# Davranış etiketleri ve class ID'leri
BEHAVIOR_LABELS = {
    0: "cow-standing",
    1: "cow-lying", 
    2: "calf-suckling",
    3: "cow-ruminating"
}

# YOLOv8 model ayarları
MODEL_CONFIG = {
    "model_path": "yolov8n.pt",  # Başlangıç için nano model
    "confidence_threshold": 0.5,
    "iou_threshold": 0.45
}

# Log ayarları
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "session_log": DATASET_CONFIG["logs_dir"] / "session.log",
    "camera_log": DATASET_CONFIG["logs_dir"] / "camera64.json"
}

# Klasörleri oluştur
def create_directories():
    """Gerekli klasörleri oluşturur"""
    for dir_path in DATASET_CONFIG.values():
        if isinstance(dir_path, Path):
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✓ Klasör oluşturuldu: {dir_path}")

if __name__ == "__main__":
    create_directories()
