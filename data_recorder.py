"""
Veri <PERSON>t Mo<PERSON>ülü
Etiketli görüntüleri ve label dosyalarını kaydeder
"""

import cv2
import json
import logging
from datetime import datetime
from pathlib import Path
from config import DATASET_CONFIG, CAPTURE_CONFIG

class DataRecorder:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.frame_counter = 0
        self.session_start = datetime.now()
        self.stats = {
            'total_frames_saved': 0,
            'behaviors_detected': {
                'cow-standing': 0,
                'cow-lying': 0,
                'calf-suckling': 0,
                'cow-ruminating': 0
            },
            'session_start': self.session_start.isoformat()
        }
        
        # Klasörleri oluştur
        self._create_directories()
        
    def _create_directories(self):
        """Gerekli klasörleri oluşturur"""
        for dir_path in DATASET_CONFIG.values():
            if isinstance(dir_path, Path):
                dir_path.mkdir(parents=True, exist_ok=True)
        self.logger.info("Veri kayıt klasörleri hazırlandı")
    
    def save_frame_with_labels(self, frame, analysis_result):
        """
        Frame'i ve etiketlerini kaydeder
        
        Args:
            frame: OpenCV frame
            analysis_result: BehaviorAnalyzer'dan gelen sonuç
        """
        if not analysis_result['has_behaviors']:
            return False
            
        try:
            self.frame_counter += 1
            
            # Dosya adı oluştur
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"frame_{self.frame_counter:05d}_{timestamp}"
            
            # Görüntüyü kaydet
            image_path = DATASET_CONFIG['images_dir'] / f"{filename}.jpg"
            success = cv2.imwrite(
                str(image_path), 
                frame, 
                [cv2.IMWRITE_JPEG_QUALITY, CAPTURE_CONFIG['quality']]
            )
            
            if not success:
                self.logger.error(f"Görüntü kaydedilemedi: {image_path}")
                return False
            
            # Label dosyasını kaydet
            label_path = DATASET_CONFIG['labels_dir'] / f"{filename}.txt"
            with open(label_path, 'w') as f:
                for label in analysis_result['yolo_labels']:
                    f.write(label + '\n')
            
            # İstatistikleri güncelle
            self._update_stats(analysis_result['detections'])
            
            self.logger.info(f"✓ Veri kaydedildi: {filename}")
            self.logger.info(f"  - Davranışlar: {[d['behavior'] for d in analysis_result['detections']]}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Veri kayıt hatası: {e}")
            return False
    
    def _update_stats(self, detections):
        """İstatistikleri günceller"""
        self.stats['total_frames_saved'] += 1
        
        for detection in detections:
            behavior = detection['behavior']
            if behavior in self.stats['behaviors_detected']:
                self.stats['behaviors_detected'][behavior] += 1
    
    def save_session_stats(self):
        """Oturum istatistiklerini JSON olarak kaydeder"""
        try:
            self.stats['session_end'] = datetime.now().isoformat()
            self.stats['session_duration_minutes'] = (
                datetime.now() - self.session_start
            ).total_seconds() / 60
            
            stats_path = DATASET_CONFIG['logs_dir'] / 'camera64.json'
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✓ Oturum istatistikleri kaydedildi: {stats_path}")
            
        except Exception as e:
            self.logger.error(f"İstatistik kayıt hatası: {e}")
    
    def get_stats(self):
        """Güncel istatistikleri döndürür"""
        return self.stats.copy()
    
    def print_stats(self):
        """İstatistikleri konsola yazdırır"""
        print("\n" + "="*50)
        print("📊 OTURUM İSTATİSTİKLERİ")
        print("="*50)
        print(f"Toplam kaydedilen frame: {self.stats['total_frames_saved']}")
        print(f"Oturum başlangıcı: {self.stats['session_start']}")
        print("\n🐄 Tespit edilen davranışlar:")
        
        for behavior, count in self.stats['behaviors_detected'].items():
            if count > 0:
                print(f"  - {behavior}: {count} adet")
        
        print("="*50)

if __name__ == "__main__":
    # Test kodu
    import numpy as np
    logging.basicConfig(level=logging.INFO)
    
    recorder = DataRecorder()
    
    # Test frame ve analiz sonucu
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    test_result = {
        'has_behaviors': True,
        'detections': [{'behavior': 'cow-standing', 'confidence': 0.85}],
        'yolo_labels': ['0 0.5 0.5 0.3 0.4']
    }
    
    recorder.save_frame_with_labels(test_frame, test_result)
    recorder.print_stats()
