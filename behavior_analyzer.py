"""
<PERSON><PERSON> Analizi Modülü
YOLOv8 kullanarak hayvan davranışlarını tespit eder
"""

import cv2
import numpy as np
import logging
from ultralytics import YOLO
from config import MODEL_CONFIG, BEHAVIOR_LABELS

class BehaviorAnalyzer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.load_model()
        
    def load_model(self):
        """YOLOv8 modelini yükler"""
        try:
            self.logger.info(f"Model yükleniyor: {MODEL_CONFIG['model_path']}")
            self.model = YOLO(MODEL_CONFIG['model_path'])
            self.logger.info("✓ Model başarıyla yüklendi")
            return True
        except Exception as e:
            self.logger.error(f"Model yükleme hatası: {e}")
            return False
    
    def analyze_frame(self, frame):
        """
        Frame'i analiz eder ve davranışları tespit eder
        
        Args:
            frame: OpenCV frame (numpy array)
            
        Returns:
            dict: {
                'has_behaviors': bool,
                'detections': list,
                'yolo_labels': list
            }
        """
        if self.model is None:
            self.logger.error("Model yüklü değil!")
            return {'has_behaviors': False, 'detections': [], 'yolo_labels': []}
        
        try:
            # YOLOv8 ile tahmin
            results = self.model(frame, 
                               conf=MODEL_CONFIG['confidence_threshold'],
                               iou=MODEL_CONFIG['iou_threshold'],
                               verbose=False)
            
            detections = []
            yolo_labels = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Koordinatları al
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        
                        # Davranış analizi (geçici olarak genel nesneleri davranışa çevir)
                        behavior = self._map_to_behavior(class_id, x1, y1, x2, y2, frame)
                        
                        if behavior:
                            detection = {
                                'behavior': behavior,
                                'confidence': float(confidence),
                                'bbox': [float(x1), float(y1), float(x2), float(y2)]
                            }
                            detections.append(detection)
                            
                            # YOLO formatına çevir
                            yolo_label = self._convert_to_yolo_format(
                                behavior, x1, y1, x2, y2, frame.shape
                            )
                            yolo_labels.append(yolo_label)
            
            has_behaviors = len(detections) > 0
            
            if has_behaviors:
                self.logger.info(f"Davranış tespit edildi: {len(detections)} adet")
            
            return {
                'has_behaviors': has_behaviors,
                'detections': detections,
                'yolo_labels': yolo_labels
            }
            
        except Exception as e:
            self.logger.error(f"Frame analiz hatası: {e}")
            return {'has_behaviors': False, 'detections': [], 'yolo_labels': []}
    
    def _map_to_behavior(self, class_id, x1, y1, x2, y2, frame):
        """
        Genel nesne sınıfını davranışa çevirir (geçici çözüm)
        Gerçek uygulamada özel eğitilmiş model kullanılacak
        """
        # COCO dataset class mapping (geçici)
        coco_classes = {
            0: 'person',
            16: 'bird', 
            17: 'cat',
            18: 'dog',
            19: 'horse',
            20: 'sheep',
            21: 'cow'  # İnek sınıfı
        }
        
        if class_id == 21:  # İnek tespit edildi
            # Basit heuristik ile davranış tahmini
            bbox_height = y2 - y1
            bbox_width = x2 - x1
            aspect_ratio = bbox_width / bbox_height if bbox_height > 0 else 0
            
            # Geçici davranış tahmini (gerçekte daha karmaşık olacak)
            if aspect_ratio > 1.5:
                return "cow-lying"  # Yatıyor
            elif y2 > frame.shape[0] * 0.7:  # Alt kısımda
                return "cow-standing"  # Ayakta
            else:
                return "cow-ruminating"  # Geviş getiriyor
                
        elif class_id == 0:  # İnsan tespit edildi (buzağı yerine geçici)
            return "calf-suckling"
            
        return None
    
    def _convert_to_yolo_format(self, behavior, x1, y1, x2, y2, frame_shape):
        """
        Bounding box'ı YOLO formatına çevirir
        
        Returns:
            str: "class_id x_center y_center width height" (normalized)
        """
        # Davranış class ID'sini bul
        class_id = None
        for cid, label in BEHAVIOR_LABELS.items():
            if label == behavior:
                class_id = cid
                break
        
        if class_id is None:
            class_id = 0  # Varsayılan
        
        # Normalize et
        h, w = frame_shape[:2]
        x_center = ((x1 + x2) / 2) / w
        y_center = ((y1 + y2) / 2) / h
        width = (x2 - x1) / w
        height = (y2 - y1) / h
        
        return f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}"

if __name__ == "__main__":
    # Test kodu
    logging.basicConfig(level=logging.INFO)
    
    analyzer = BehaviorAnalyzer()
    
    # Test frame oluştur
    test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    result = analyzer.analyze_frame(test_frame)
    print(f"Test sonucu: {result}")
