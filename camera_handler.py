"""
RTSP Kamera Bağlantı ve Frame Yakalama Modülü
"""

import cv2
import time
import logging
from threading import Thread, Event
from config import CAMERA_CONFIG, RTSP_URL_TEMPLATE, CAPTURE_CONFIG, DEMO_MODE

class CameraHandler:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rtsp_url = self._build_rtsp_url()
        self.cap = None
        self.is_connected = False
        self.stop_event = Event()
        self.current_frame = None
        self.frame_count = 0
        
    def _build_rtsp_url(self):
        """RTSP URL'ini oluşturur"""
        return RTSP_URL_TEMPLATE.format(**CAMERA_CONFIG)
    
    def connect(self):
        """Kameraya bağlanır"""
        try:
            if DEMO_MODE:
                self.logger.info("🎥 Demo modu: Webcam kullanılıyor")
                self.cap = cv2.VideoCapture(0)  # Webcam
            else:
                self.logger.info(f"Kameraya bağlanılıyor: {CAMERA_CONFIG['ip']}")
                self.cap = cv2.VideoCapture(self.rtsp_url)

            # Kamera ayarları
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, CAPTURE_CONFIG['frame_width'])
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, CAPTURE_CONFIG['frame_height'])
            self.cap.set(cv2.CAP_PROP_FPS, CAPTURE_CONFIG['fps'])

            # Bağlantı testi
            ret, frame = self.cap.read()
            if ret and frame is not None:
                self.is_connected = True
                mode_text = "Webcam" if DEMO_MODE else "RTSP Kamera"
                self.logger.info(f"✓ {mode_text} bağlantısı başarılı")
                return True
            else:
                mode_text = "Webcam" if DEMO_MODE else "RTSP Kamera"
                self.logger.error(f"✗ {mode_text} bağlantısı başarısız")
                return False

        except Exception as e:
            self.logger.error(f"Kamera bağlantı hatası: {e}")
            return False
    
    def start_capture(self):
        """Frame yakalama işlemini başlatır"""
        if not self.is_connected:
            self.logger.error("Kamera bağlı değil!")
            return False
            
        self.capture_thread = Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        self.logger.info("Frame yakalama başlatıldı")
        return True
    
    def _capture_loop(self):
        """Sürekli frame yakalama döngüsü"""
        while not self.stop_event.is_set():
            try:
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    self.current_frame = frame.copy()
                    self.frame_count += 1
                    
                    if self.frame_count % 30 == 0:  # Her 30 frame'de log
                        self.logger.debug(f"Frame yakalandı: {self.frame_count}")
                else:
                    self.logger.warning("Frame yakalanamadı, yeniden bağlanılıyor...")
                    self.reconnect()
                    
                time.sleep(1.0 / CAPTURE_CONFIG['fps'])  # FPS kontrolü
                
            except Exception as e:
                self.logger.error(f"Frame yakalama hatası: {e}")
                time.sleep(1)
    
    def get_current_frame(self):
        """Güncel frame'i döndürür"""
        return self.current_frame.copy() if self.current_frame is not None else None
    
    def reconnect(self):
        """Kameraya yeniden bağlanır"""
        self.logger.info("Yeniden bağlanılıyor...")
        self.disconnect()
        time.sleep(2)
        self.connect()
    
    def disconnect(self):
        """Kamera bağlantısını keser"""
        self.is_connected = False
        if self.cap:
            self.cap.release()
        self.logger.info("Kamera bağlantısı kesildi")
    
    def stop(self):
        """Tüm işlemleri durdurur"""
        self.stop_event.set()
        self.disconnect()
        self.logger.info("Kamera handler durduruldu")

if __name__ == "__main__":
    # Test kodu
    logging.basicConfig(level=logging.INFO)
    
    camera = CameraHandler()
    if camera.connect():
        camera.start_capture()
        
        # 10 saniye test
        for i in range(10):
            frame = camera.get_current_frame()
            if frame is not None:
                print(f"Frame {i+1}: {frame.shape}")
            time.sleep(1)
            
        camera.stop()
