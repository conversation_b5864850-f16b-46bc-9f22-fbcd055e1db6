# 🐄 Çiftlik İzleme Sistemi - Kullanım Kılavuzu

## 🚀 Hızlı Başlangıç

### 1. <PERSON><PERSON><PERSON>
```bash
# Gerekli paketleri yükle
pip install -r requirements.txt

# Ku<PERSON>lum testini çalıştır
python setup.py
```

### 2. <PERSON><PERSON>a <PERSON>ı
`config.py` dosyasın<PERSON> dü<PERSON>leyin:

```python
# Gerçek ONVIF kamera için
DEMO_MODE = False
CAMERA_CONFIG = {
    "ip": "************",      # Kameranızın IP adresi
    "username": "admin",       # <PERSON><PERSON><PERSON> kullanıcı adı
    "password": "admin123",    # Kamera şifresi
}

# Demo/Test için (webcam kullanır)
DEMO_MODE = True
```

### 3. Sistemi Başlat

#### Komut Satırı Modu
```bash
python main.py
```

#### Web Arayüzü Modu (Önerilen)
```bash
python web_app.py
```
Tarayıcınızda `http://localhost:5000` adresini açın

## 📋 Sistem Özellikleri

### ✅ Otomatik Davranış Tespiti
- **cow-standing**: İnek ayakta
- **cow-lying**: İnek yatıyor
- **cow-ruminating**: İnek geviş getiriyor
- **calf-suckling**: Buzağı emiyor

### ✅ Otomatik Veri Kayıt
- Her tespit edilen davranış için `.jpg` görüntü
- YOLO formatında `.txt` etiket dosyası
- Detaylı sistem logları

### ✅ Gerçek Zamanlı İzleme
- Saniyede 1 frame analizi
- Sürekli çalışma
- Otomatik yeniden bağlanma

## 📁 Çıktı Dosyaları

```
dataset/
├── images/           # Kaydedilen görüntüler
│   ├── frame_00001_20250709_010321.jpg
│   └── ...
├── labels/           # YOLO etiket dosyaları
│   ├── frame_00001_20250709_010321.txt
│   └── ...
└── logs/            # Sistem logları
    ├── session.log
    └── camera64.json
```

### YOLO Etiket Formatı
```
class_id x_center y_center width height
```
- Tüm değerler 0-1 arasında normalize edilmiş
- class_id: 0=cow-standing, 1=cow-lying, 2=calf-suckling, 3=cow-ruminating

## 🔧 Konfigürasyon

### Kamera Ayarları
```python
CAMERA_CONFIG = {
    "ip": "************",
    "port": 80,
    "username": "admin", 
    "password": "admin123",
    "rtsp_port": 554
}
```

### Frame Ayarları
```python
CAPTURE_CONFIG = {
    "fps": 1,              # Saniyede frame sayısı
    "frame_width": 640,    # Görüntü genişliği
    "frame_height": 480,   # Görüntü yüksekliği
    "quality": 95          # JPEG kalitesi
}
```

### Model Ayarları
```python
MODEL_CONFIG = {
    "model_path": "yolov8n.pt",
    "confidence_threshold": 0.5,
    "iou_threshold": 0.45
}
```

## 🎮 Sistem Kontrolü

### Başlatma
```bash
python main.py
```

### Durdurma
- `Ctrl+C` ile güvenli durdurma
- Sistem otomatik olarak istatistikleri kaydeder

### Test Modu
```bash
# Demo modu (webcam)
DEMO_MODE = True

# Gerçek kamera modu
DEMO_MODE = False
```

## 📊 İstatistikler

Sistem çalışırken:
- Her 60 saniyede durum raporu
- Toplam işlenen frame sayısı
- Kaydedilen frame sayısı
- Davranış tespit oranları

Oturum sonunda:
- `camera64.json` - Detaylı JSON raporu
- Davranış bazında istatistikler
- Oturum süresi ve performans

## 🐛 Sorun Giderme

### Kamera Bağlantı Sorunları
1. IP adresini ping ile test edin
2. ONVIF servisinin aktif olduğunu kontrol edin
3. Kullanıcı adı/şifre doğruluğunu kontrol edin
4. Firewall ayarlarını kontrol edin

### Webcam Sorunları (Demo Modu)
1. Webcam'in başka uygulama tarafından kullanılmadığından emin olun
2. Webcam sürücülerini kontrol edin
3. `DEMO_MODE = True` olduğunu kontrol edin

### Performans Sorunları
1. GPU kullanımı için CUDA kurulumu yapın
2. Frame çözünürlüğünü düşürün
3. Confidence threshold'u artırın

### Model Yükleme Sorunları
1. İnternet bağlantısını kontrol edin
2. Disk alanının yeterli olduğundan emin olun
3. Antivirus yazılımını geçici olarak devre dışı bırakın

## 🌐 Web Arayüzü Kullanımı

### Özellikler
- **📹 Canlı Video İzleme**: Gerçek zamanlı kamera görüntüsü
- **📊 Anlık İstatistikler**: Frame sayısı, tespit oranı, çalışma süresi
- **🧠 Davranış Analizi**: Tespit edilen davranışların canlı takibi
- **📸 Son Görüntüler**: Kaydedilen son görüntülerin galerisi
- **🎮 Sistem Kontrolü**: Başlat/Durdur butonları

### Adım Adım Kullanım
1. **Web Sunucusunu Başlatın**
   ```bash
   python web_app.py
   ```

2. **Tarayıcıda Açın**
   - `http://localhost:5000` adresini ziyaret edin
   - Mobil cihazlarda da çalışır

3. **Sistemi Kontrol Edin**
   - "▶️ Başlat" butonuna tıklayın
   - Canlı video akışını izleyin
   - İstatistikleri takip edin
   - "⏹️ Durdur" ile sistemi durdurun

### Web Arayüzü Avantajları
- Uzaktan erişim imkanı
- Görsel istatistik takibi
- Kolay sistem kontrolü
- Mobil uyumluluk
- Gerçek zamanlı bildirimler

## 🔮 Gelişmiş Kullanım

### Özel Model Eğitimi
1. `dataset/` klasöründeki verileri kullanın
2. YOLOv8 ile özel model eğitin
3. `MODEL_CONFIG["model_path"]` değerini güncelleyin

### Çoklu Kamera Desteği
- Her kamera için ayrı config dosyası oluşturun
- Paralel process'ler çalıştırın

### API Entegrasyonu
- `behavior_analyzer.py` ve `data_recorder.py` modüllerini kullanın
- REST API wrapper geliştirin

## 📞 Destek

Sorunlarınız için:
1. Log dosyalarını kontrol edin
2. GitHub Issues açın
3. Detaylı hata mesajlarını paylaşın

---

**Not**: Bu sistem geliştirme aşamasındadır. Üretim ortamında kullanmadan önce kapsamlı testler yapın.
