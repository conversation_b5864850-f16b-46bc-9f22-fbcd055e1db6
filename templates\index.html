<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐄 Çiftlik İzleme Sistemi</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .video-container {
            position: relative;
            background: #f7fafc;
            border-radius: 10px;
            overflow: hidden;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #liveVideo {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
        }

        .no-video {
            color: #a0aec0;
            font-size: 1.1em;
            text-align: center;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-start {
            background: linear-gradient(45deg, #48bb78, #38a169);
            color: white;
        }

        .btn-start:hover {
            background: linear-gradient(45deg, #38a169, #2f855a);
            transform: translateY(-2px);
        }

        .btn-stop {
            background: linear-gradient(45deg, #f56565, #e53e3e);
            color: white;
        }

        .btn-stop:hover {
            background: linear-gradient(45deg, #e53e3e, #c53030);
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .behaviors {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .behavior-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .behavior-name {
            font-weight: 600;
            color: #4a5568;
        }

        .behavior-count {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-weight: bold;
        }

        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status.running {
            background: #c6f6d5;
            color: #22543d;
        }

        .status.stopped {
            background: #fed7d7;
            color: #742a2a;
        }

        .recent-images {
            grid-column: 1 / -1;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .image-item {
            background: #f7fafc;
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .image-item:hover {
            transform: scale(1.05);
        }

        .image-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .image-info {
            padding: 10px;
            text-align: center;
            font-size: 0.9em;
            color: #718096;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert.success {
            background: #c6f6d5;
            color: #22543d;
            border-left: 4px solid #48bb78;
        }

        .alert.error {
            background: #fed7d7;
            color: #742a2a;
            border-left: 4px solid #f56565;
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐄 Çiftlik İzleme Sistemi</h1>
            <p>Gerçek zamanlı hayvan davranışı analizi ve otomatik etiketleme</p>
        </div>

        <div id="alertContainer"></div>

        <div class="dashboard">
            <!-- Canlı Video -->
            <div class="card">
                <h3>📹 Canlı İzleme</h3>
                <div class="video-container">
                    <img id="liveVideo" style="display: none;" alt="Canlı Video">
                    <div id="noVideo" class="no-video">
                        <p>📷 Video akışı bekleniyor...</p>
                        <p>Sistemi başlatın</p>
                    </div>
                </div>
                <div class="controls">
                    <button id="startBtn" class="btn btn-start">▶️ Başlat</button>
                    <button id="stopBtn" class="btn btn-stop" disabled>⏹️ Durdur</button>
                </div>
            </div>

            <!-- İstatistikler -->
            <div class="card">
                <h3>📊 Sistem İstatistikleri</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalFrames">0</div>
                        <div class="stat-label">Toplam Frame</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="savedFrames">0</div>
                        <div class="stat-label">Kaydedilen</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="detectionRate">0%</div>
                        <div class="stat-label">Tespit Oranı</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="uptime">00:00:00</div>
                        <div class="stat-label">Çalışma Süresi</div>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <strong>Durum: </strong>
                    <span id="systemStatus" class="status stopped">Durduruldu</span>
                </div>
            </div>

            <!-- Davranış Analizi -->
            <div class="card">
                <h3>🧠 Tespit Edilen Davranışlar</h3>
                <div class="behaviors">
                    <div class="behavior-item">
                        <span class="behavior-name">🐄 İnek Ayakta</span>
                        <span class="behavior-count" id="cowStanding">0</span>
                    </div>
                    <div class="behavior-item">
                        <span class="behavior-name">😴 İnek Yatıyor</span>
                        <span class="behavior-count" id="cowLying">0</span>
                    </div>
                    <div class="behavior-item">
                        <span class="behavior-name">🍼 Buzağı Emiyor</span>
                        <span class="behavior-count" id="calfSuckling">0</span>
                    </div>
                    <div class="behavior-item">
                        <span class="behavior-name">🌿 Geviş Getiriyor</span>
                        <span class="behavior-count" id="cowRuminating">0</span>
                    </div>
                </div>
            </div>

            <!-- Son Görüntüler -->
            <div class="card recent-images">
                <h3>📸 Son Kaydedilen Görüntüler</h3>
                <div id="recentImages" class="images-grid">
                    <p class="no-video">Henüz kaydedilen görüntü yok</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Socket.IO bağlantısı
        const socket = io();
        
        // DOM elementleri
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const liveVideo = document.getElementById('liveVideo');
        const noVideo = document.getElementById('noVideo');
        const alertContainer = document.getElementById('alertContainer');
        
        // Buton event listeners
        startBtn.addEventListener('click', startMonitoring);
        stopBtn.addEventListener('click', stopMonitoring);
        
        // Socket event listeners
        socket.on('connect', function() {
            console.log('WebSocket bağlantısı kuruldu');
            loadStatus();
        });
        
        socket.on('new_frame', function(data) {
            liveVideo.src = data.image;
            liveVideo.style.display = 'block';
            noVideo.style.display = 'none';
        });
        
        socket.on('stats_update', function(stats) {
            updateStats(stats);
        });
        
        // Fonksiyonlar
        async function startMonitoring() {
            try {
                startBtn.disabled = true;
                const response = await fetch('/api/start', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Sistem başarıyla başlatıldı!', 'success');
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                } else {
                    showAlert(result.message, 'error');
                    startBtn.disabled = false;
                }
            } catch (error) {
                showAlert('Bağlantı hatası: ' + error.message, 'error');
                startBtn.disabled = false;
            }
        }
        
        async function stopMonitoring() {
            try {
                stopBtn.disabled = true;
                const response = await fetch('/api/stop', { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    showAlert('Sistem durduruldu', 'success');
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    liveVideo.style.display = 'none';
                    noVideo.style.display = 'block';
                } else {
                    showAlert(result.message, 'error');
                    stopBtn.disabled = false;
                }
            } catch (error) {
                showAlert('Bağlantı hatası: ' + error.message, 'error');
                stopBtn.disabled = false;
            }
        }
        
        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                if (status.is_running) {
                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                } else {
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
                
                updateStats(status.stats);
                loadRecentImages();
            } catch (error) {
                console.error('Durum yükleme hatası:', error);
            }
        }
        
        function updateStats(stats) {
            document.getElementById('totalFrames').textContent = stats.total_frames;
            document.getElementById('savedFrames').textContent = stats.saved_frames;
            document.getElementById('detectionRate').textContent = stats.detection_rate + '%';
            document.getElementById('uptime').textContent = stats.uptime;
            
            // Sistem durumu
            const statusElement = document.getElementById('systemStatus');
            statusElement.textContent = stats.status;
            statusElement.className = 'status ' + (stats.status === 'Çalışıyor' ? 'running' : 'stopped');
            
            // Davranışlar
            document.getElementById('cowStanding').textContent = stats.behaviors['cow-standing'];
            document.getElementById('cowLying').textContent = stats.behaviors['cow-lying'];
            document.getElementById('calfSuckling').textContent = stats.behaviors['calf-suckling'];
            document.getElementById('cowRuminating').textContent = stats.behaviors['cow-ruminating'];
        }
        
        async function loadRecentImages() {
            try {
                const response = await fetch('/api/recent_images');
                const images = await response.json();
                
                const container = document.getElementById('recentImages');
                
                if (images.length === 0) {
                    container.innerHTML = '<p class="no-video">Henüz kaydedilen görüntü yok</p>';
                    return;
                }
                
                container.innerHTML = images.map(img => `
                    <div class="image-item">
                        <img src="${img.path}" alt="${img.filename}" loading="lazy">
                        <div class="image-info">${img.timestamp}</div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Görüntü yükleme hatası:', error);
            }
        }
        
        function showAlert(message, type) {
            const alert = document.createElement('div');
            alert.className = `alert ${type}`;
            alert.textContent = message;
            
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                alert.remove();
            }, 5000);
        }
        
        // Sayfa yüklendiğinde
        document.addEventListener('DOMContentLoaded', function() {
            loadStatus();
            
            // Her 30 saniyede bir son görüntüleri yenile
            setInterval(loadRecentImages, 30000);
        });
    </script>
</body>
</html>
