"""
🐄 Çiftlik İzleme Sistemi - Demo Script
Hızlı test ve demo için kullanılır
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def print_banner():
    """Demo banner'ını yazdırır"""
    print("=" * 60)
    print("🐄 ÇİFTLİK İZLEME SİSTEMİ - DEMO")
    print("=" * 60)
    print("Bu demo scripti sistemi hızlıca test etmenizi sağlar.")
    print("Demo modu aktif - Webcam kullanılacak")
    print("=" * 60)

def check_requirements():
    """Gereksinimleri kontrol eder"""
    print("\n🔍 Gereksinimler kontrol ediliyor...")
    
    try:
        import cv2
        import numpy
        import ultralytics
        import flask
        print("✅ Tüm paketler yüklü")
        return True
    except ImportError as e:
        print(f"❌ Eksik paket: {e}")
        print("Lütfen önce 'pip install -r requirements.txt' çalıştırın")
        return False

def setup_demo_config():
    """Demo için config ayarlarını yapar"""
    print("\n⚙️ Demo konfigürasyonu ayarlanıyor...")
    
    # config.py'yi güncelle
    config_path = Path("config.py")
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Demo mode'u aktif et
        content = content.replace('DEMO_MODE = False', 'DEMO_MODE = True')
        
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Demo modu aktifleştirildi")
    else:
        print("❌ config.py bulunamadı")
        return False
    
    return True

def create_directories():
    """Gerekli klasörleri oluşturur"""
    print("\n📁 Klasörler oluşturuluyor...")
    
    try:
        from config import create_directories
        create_directories()
        print("✅ Klasörler hazır")
        return True
    except Exception as e:
        print(f"❌ Klasör oluşturma hatası: {e}")
        return False

def run_web_demo():
    """Web demo'sunu çalıştırır"""
    print("\n🌐 Web arayüzü başlatılıyor...")
    print("📱 Tarayıcınızda http://localhost:5000 adresini açın")
    print("🎮 Web arayüzünden '▶️ Başlat' butonuna tıklayın")
    print("⏹️ Durdurmak için bu terminalde Ctrl+C basın")
    print("-" * 60)
    
    try:
        # Web uygulamasını başlat
        subprocess.run([sys.executable, "web_app.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Demo durduruldu")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Web uygulaması hatası: {e}")
    except FileNotFoundError:
        print("\n❌ web_app.py bulunamadı")

def run_cli_demo():
    """Komut satırı demo'sunu çalıştırır"""
    print("\n💻 Komut satırı modu başlatılıyor...")
    print("⏹️ Durdurmak için Ctrl+C basın")
    print("-" * 60)
    
    try:
        # Ana uygulamayı başlat
        subprocess.run([sys.executable, "main.py"], check=True)
    except KeyboardInterrupt:
        print("\n\n🛑 Demo durduruldu")
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Uygulama hatası: {e}")
    except FileNotFoundError:
        print("\n❌ main.py bulunamadı")

def show_menu():
    """Ana menüyü gösterir"""
    print("\n📋 DEMO SEÇENEKLERİ:")
    print("1. 🌐 Web Arayüzü Demo (Önerilen)")
    print("2. 💻 Komut Satırı Demo")
    print("3. 🚪 Çıkış")
    print("-" * 60)
    
    while True:
        try:
            choice = input("Seçiminizi yapın (1-3): ").strip()
            
            if choice == "1":
                return "web"
            elif choice == "2":
                return "cli"
            elif choice == "3":
                return "exit"
            else:
                print("❌ Geçersiz seçim! Lütfen 1, 2 veya 3 girin.")
        except KeyboardInterrupt:
            return "exit"

def main():
    """Ana demo fonksiyonu"""
    print_banner()
    
    # Gereksinimleri kontrol et
    if not check_requirements():
        return
    
    # Demo config ayarla
    if not setup_demo_config():
        return
    
    # Klasörleri oluştur
    if not create_directories():
        return
    
    # Menüyü göster
    choice = show_menu()
    
    if choice == "web":
        run_web_demo()
    elif choice == "cli":
        run_cli_demo()
    elif choice == "exit":
        print("\n👋 Demo sonlandırıldı. İyi günler!")
    
    print("\n" + "=" * 60)
    print("📚 Daha fazla bilgi için:")
    print("   - README.md")
    print("   - KULLANIM_KILAVUZU.md")
    print("=" * 60)

if __name__ == "__main__":
    main()
